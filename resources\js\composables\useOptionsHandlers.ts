import { debounce } from 'lodash'
import useOptions from '@/composables/option'
import useFields from '@/composables/field'

export default function useOptionsHandlers() {
	const { getUsers, getDepartments, getWorkflows, getScopes } = useOptions()
	const { getColumnDatas } = useFields()

	const getOptionWorkflows = async (query: string) => {
		try {
			let result = await getWorkflows(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: elem.name,
						formId: elem.form_id,
						jobManager: elem.job_manager,
						followers: elem.followers,
                    }
                ));
            }
            return []; // Luôn trả về array
		} catch (error) {
			console.error('Error fetching workflows:', error);
			return [];
		}
	}

	const getOptionUsers = async (query: string) => {
		try {
			let result = await getUsers(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: `${elem.account_name} - ${elem.full_name}`,
                    }
                ));
            }
            return []; // Luôn trả về array
		} catch (error) {
			console.error('Error fetching users:', error);
			return [];
		}
	}

	const getOptionDepartments = async (selectOptionDepartments: any[]) => {
        let result = await getDepartments();
        if (Array.isArray(result) && result.length > 0) {
            selectOptionDepartments.splice(0, selectOptionDepartments.length, ...result.map((elem: any) => ({
				value: elem.id,
				label: elem.name,
				type: elem.type,
			})));
        }
	}

	const getOptionProcessScopes = async (query: string, selectOptionSystemDefaults: any[]) => {
		try {
            let result = await getScopes(query);
            if (Array.isArray(result) && result.length > 0) {
                // Kết hợp các option mặc định với kết quả trả về từ API
                return [...selectOptionSystemDefaults, ...result];
            }

            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
            return [...selectOptionSystemDefaults];
		} catch (error) {
			console.error('Error fetching scopes:', error);
			return [...selectOptionSystemDefaults];
		}
	}

	const getOptionColumnData = async (query: string, field: any) => {
		try {
			let result = await getColumnDatas(field.object_table, field.sub_column_table, field.column_table, query);

			if (!!result && Array.isArray(result.data_options) && result.data_options.length > 0) {
                return result.data_options.map((elem: any) => (
					{
						value: elem.id,
						label: elem[field.column_table],
						sub_column_table_description: result.sub_column_table_description,
						sub_column_table_data_options: result.data_options
					}
				));
            }
            return []; // Luôn trả về array
		} catch (error) {
			console.error('Error fetching column data:', error);
			return [];
		}
	}

	return {
		getOptionWorkflows: debounce(getOptionWorkflows, 500),
		getOptionUsers: debounce(getOptionUsers, 500),
		getOptionDepartments,
		getOptionProcessScopes: debounce(getOptionProcessScopes, 500),
		getOptionColumnData: debounce(getOptionColumnData, 500)
	}
}
