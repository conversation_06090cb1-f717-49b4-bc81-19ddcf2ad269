import { useI18n } from "vue-i18n"
import { numberCommas } from "@/utils/utils"
import { evaluate } from 'mathjs'

export default function useDynamicFormLogic() {
	const { t } = useI18n()

	const transformObject = (obj: object) => {
		return Object.fromEntries(
			Object.entries(obj).map(([key, value]) => {
				if (value === 1) return [key, true];
				if (value === 0) return [key, false];
				if (typeof value === 'string' && isJsonArrayOrObject(value)) {
					return [key, JSON.parse(value)];
				}
				if (typeof value === 'string') {
					return [key, value];
				}
				return [key, value];
			})
		);
	};

	const isJsonArrayOrObject = (str: any) => {
		try {
			const parsed = JSON.parse(str);
			return typeof parsed === 'object' && parsed !== null;
		} catch (e) {
			return false;
		}
	};

	const mappedTypeDataField = (typeField: string) => {
		switch (typeField) {
			case 'VARCHAR':
				return 'text';
			case 'TEXT':
				return 'textarea';
			case 'INTEGER':
				return 'number';
			case 'FLOAT':
				return 'number';
			case 'DATE':
				return 'date';
			case 'TIME':
				return 'time';
			case 'SELECT':
				return 'MULTISELECT';
			case 'RADIO':
				return 'radio';
			case 'CHECKLIST':
				return 'checkbox';
			default:
				return typeField;
		}
	}

	const converFormFields = (dataFormFields: any, fieldTable: boolean) => {
		// VARCHAR, TEXT, INTEGER, FLOAT, DATE, TIME, SELECT, RADIO, CHECKLIST, DEPARTMENT, USER, FILEUPLOAD, FORMULA, TABLE, OBJECTSYSTEM
		if (dataFormFields.length > 0) {
			const sortedDataFields = dataFormFields.filter((item: any) => fieldTable ? item.parent_id !== null : item.parent_id === null).slice().sort((a: any, b: any) => a.order - b.order);
			const transformedData = sortedDataFields.map((dataField: any) => {
				// Lấy ngôn ngữ được chọn
				const currentLanguage: string = localStorage.getItem('app_language') || 'vi';
				// Lấy label dựa trên ngôn ngữ hiện tại
				const labelKey = currentLanguage === 'vi' ? 'display_name' : 'display_name_en';
				const placeholderKey = currentLanguage === 'vi' ? 'placeholder' : 'placeholder_en';
				const item = transformObject(dataField);
				
				// Initialize validation and validationMessages
				let validation = item.required ? 'required' : '';
				let validationMessages = {} as any;

				// Kiểm tra type và xử lý các thuộc tính khác nhau
				const formField = {
					type: mappedTypeDataField(item.type),
					label: item[labelKey],
					placeholder: item[placeholderKey],
					floatingLabel: 'true',
					class: 'form-control',
					name: item.keyword,
					validation: validation,
					validationMessages: validationMessages,
					column_width: item.column_width,
				} as any;
				
				// Chỉ thêm validationMessages.required nếu có required
				if (item.required) {
					if (item.type === 'RADIO' || item.type === 'CHECKLIST') {
						formField.legendClass = 'required-label';
					} else {
						formField.labelClass = 'required-label';
					}
					validationMessages.required = `${item[labelKey]} ${t('validate_field.display_name.required')}`;
				}

				// Nếu có default_value thì thêm vào formField
				if (item.default_value !== null) {
					formField.value = item.default_value;
				}

				// Nếu có not_edit thì thêm vào formField
				if (item.not_edit) {
					formField.disabled = item.not_edit;
				}

				// Nếu type là "RADIO", "CHECKLIST", "TIME", "DATE", thiết lập floatingLabel thành false
				if (item.type === 'RADIO' || item.type === 'CHECKLIST' || item.type === 'DATE' || item.type === 'TIME') {
					formField.floatingLabel = 'false';
				}

				// Chỉ thêm length khi có min_equal và max_equal cho type "text" và "textarea"
				if ((item.type === 'VARCHAR' || item.type === 'TEXT') && item.min_equal != null && item.max_equal != null) {
					const min = parseInt(item.min_equal, 10);
					const max = parseInt(item.max_equal, 10);
					
					if (!isNaN(min) && !isNaN(max)) {
						validation += (validation ? '|' : '') + `length:${min},${max}`;
						validationMessages.length = `${item[labelKey]} ${t('validate_field.display_name.must_between_text')} ${min} ${t('validate_field.display_name.to')} ${max} ${t('validate_field.display_name.characters')}`;
					}
				}

				// Thêm validation 'between' cho type 'number'
				if (item.type === 'INTEGER' && item.min_equal != null && item.max_equal != null) {
					const min = parseInt(item.min_equal, 10);
					const max = parseInt(item.max_equal, 10);

					if (!isNaN(min) && !isNaN(max)) {
						validation += (validation ? '|' : '') + `between:${min},${max}`;
						validationMessages.between = `${item[labelKey]} ${t('validate_field.display_name.must_between_number')} ${min} ${t('validate_field.display_name.to')} ${max}`;
					}
				}

				// Gán lại giá trị của validation vào formField nếu có
				if (validation) {
					formField.validation = validation;
				}

				// Bỏ các key không có giá trị
				Object.keys(formField).forEach(key => {
					if (!formField[key] && formField[key] !== 0) {
						delete formField[key]; // Loại bỏ key nếu không có giá trị
					}
				});
				
				// Thêm options cho type 'RADIO' và 'CHECKLIST'
				if ((item.type === 'RADIO' || item.type === 'CHECKLIST') && item.options) {
					formField.options = item.options;
				}

				// Thêm options và multiple cho type 'select'
				if (item.type === 'SELECT') {
					formField.options = item.options;
					// Nếu có multiple thì thêm vào formField
					formField.multiple = item.multiple;
					// Nếu có not_edit thì thêm vào formField
					formField.disabled = item.not_edit;
				}

				if (item.type === 'USER' || item.type === 'DEPARTMENT' || item.type === 'FILEUPLOAD' || item.type === 'OBJECTSYSTEM') {
					// Nếu có multiple thì thêm vào formField
					formField.multiple = item.multiple;
					// Nếu có not_edit thì thêm vào formField
					formField.disabled = item.not_edit;
				}

				// Nếu có parent_id thì thêm vào formField
				if (item.parent_id !== null) {
					formField.parent_id = item.parent_id;
				}

				// Nếu có children thì thêm vào formField
				if (item.children.length > 0) {
					formField.childrens = item.children.map((child: any) => {
						return transformObject(child);
					});
				}

				if (item.type === 'OBJECTSYSTEM') {
					// Nếu có column_table thì thêm vào formField
					if (item.column_table !== null) {
						formField.column_table = item.column_table;
					}
					// Nếu có column_table thì thêm vào formField
					if (item.object_table !== null) {
						formField.object_table = item.object_table;
					}
					// Nếu có sub_column_table thì thêm vào formField
					if (item.sub_column_table !== null) {
						formField.sub_column_table = item.sub_column_table;
					}
				}
				
				return formField;
			});

			return transformedData;
		} else {
			return [];
		}
	}

	const removeCommas = (value: any) => {
		return value.replace(/,/g,'');
	}

	const initializeItem = (childrens: any) => {
		const newItem = {} as any;
		childrens.forEach((child: any) => {
			newItem[child.keyword] = child.default_value;
			if (typeof child.default_value === 'string' && child.default_value.startsWith('=')) {
				newItem['keyword_formula'] = child.keyword;
				newItem['original_formula'] = child.default_value;
			}
		});

		return newItem;
	};

	const initializeItemChildrens = (transformedFields: any[], itemChildrens: { [key: string]: any[] }) => {
		transformedFields.forEach((field: any) => {
			if (field.type === 'TABLE' && field.childrens) {
				itemChildrens[field.name] = [{ ...initializeItem(field.childrens) }];
			}
		});
	};

	const calculateFormulaWithFormData = (formula: string, results: any, formData: any) => {
		try {
			// Loại bỏ dấu '=' ở đầu công thức
			let expr = formula.replace('=', '');

			// Kết hợp dữ liệu từ formData và kết quả công thức đã tính
			const data = { ...formData, ...results };

			// Thay thế các biến trong công thức bằng giá trị thực tế
			expr = expr.replace(/\b[a-zA-Z_]\w*\b/g, (name) => {
				const value = data[name];
				// console.log(`Thay thế biến "${name}" bằng giá trị:`, value);
				
				return value !== undefined && value !== '' ? value : 0;
			});

			// console.log('Biểu thức sau khi thay thế:', expr);

			// Tính toán biểu thức
			const result = evaluate(expr);
			// console.log('Kết quả tính toán:', result);
			return result;
		} catch (error) {
			console.error('Error calculating formula:', error);
			return 0;
		}
	};

	const extractSumPlus = (str: string) => {
		// Định nghĩa RegEx để tìm phần trong ngoặc
		// Tìm nội dung trong ngoặc đơn, có tiền tố "@SUMPLUS"
		const regex = /\(([^)]+)\)/;
		// const regex = /@SUMPLUS\(([^)]+)\)/;
		const match = str.match(regex);

		if (match) {
			const insideParentheses = match[1];

			// Tách chuỗi bằng dấu chấm
			const [value1, value2] = insideParentheses.split('.');

			// Kiểm tra nếu có đủ hai giá trị
			if (value1 && value2) {
				return [value1, value2];
			} else {
				return null;
			}
		}

		return null;
	};

	const sumColumn = (field: any, itemChildrens: any) => {
		const SUMPLUS = field.value;
		const result = extractSumPlus(SUMPLUS);
		if (result) {
			const [tableName, columnName] = result;
			return itemChildrens[tableName].reduce((total: number, itemChildren: any) => {
				const value = parseFloat(removeCommas(itemChildren[columnName])) || 0;
				return total + value;
			}, 0);
		} else {
			console.log("Không thể bóc tách các giá trị.");
			return 0;
		}
	};

	const initializeValues = (formFields: any[], formData: any) => {
		formFields.forEach((field: any) => {
			if (field.value) {
				formData[field.name] = field.value;
			}
		});
	};

	const addItem = (field: any, itemChildrens: any) => {
		if (!itemChildrens[field.name]) {
			itemChildrens[field.name] = [];
		}
		itemChildrens[field.name].push({ ...initializeItem(field.childrens) });
	};

	const removeItem = (field: any, itemIndex: number, itemChildrens: any) => {
		if (itemChildrens[field.name] && itemChildrens[field.name][itemIndex]) {
			itemChildrens[field.name].splice(itemIndex, 1);
		}
	};

	const showSubColumnTable = (optionSelected: any, keyName: string, subColumnTableDescription: any, subColumnTableOptionSelected: any) => {
		// Reset dữ liệu trước khi cập nhật
		subColumnTableDescription[keyName] = {};
		subColumnTableOptionSelected[keyName] = {};

		// Kiểm tra optionSelected khác null va đối tượng khác rỗng
		if (Array.isArray(optionSelected) == false) {
			if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
				subColumnTableDescription[keyName] = optionSelected.sub_column_table_description;
				const subColumnTableOptionSelectedItem = optionSelected.sub_column_table_data_options.find(
					(option: any) => option.id === optionSelected.value
				);
				if (subColumnTableOptionSelectedItem) {
					subColumnTableOptionSelected[keyName] = subColumnTableOptionSelectedItem;
				}
			}
		}
	};

	const showSubColumnTableChildren = (
		optionSelected: any,
		itemIndex: number,
		keyName: string,
		subColumnTableDescriptionChildren: any,
		subColumnTableOptionSelectedChildren: any
	) => {
		// Khởi tạo nếu chưa tồn tại
		if (!subColumnTableDescriptionChildren[keyName]) {
			subColumnTableDescriptionChildren[keyName] = {};
		}
		if (!subColumnTableOptionSelectedChildren[keyName]) {
			subColumnTableOptionSelectedChildren[keyName] = {};
		}

		// Reset dữ liệu trước khi cập nhật
		subColumnTableDescriptionChildren[keyName][itemIndex] = {};
		subColumnTableOptionSelectedChildren[keyName][itemIndex] = {};

		// Kiểm tra optionSelected có phải là array hay không
		if (Array.isArray(optionSelected) == false) {
			// Kiểm tra optionSelected khác null và đối tượng khác rỗng
			if (optionSelected !== null && Object.keys(optionSelected).length !== 0) {
				subColumnTableDescriptionChildren[keyName][itemIndex] = optionSelected.sub_column_table_description;
				const subColumnTableOptionSelectedChildrenItem = optionSelected.sub_column_table_data_options.find(
					(option: any) => option.id === optionSelected.value
				);
				if (subColumnTableOptionSelectedChildrenItem) {
					subColumnTableOptionSelectedChildren[keyName][itemIndex] = subColumnTableOptionSelectedChildrenItem;
				}
			}
		}
	};

	const updateFiles = (fileItemUploads: any, fieldName: string, formData: any) => {
		formData[fieldName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem: any) => fileItem.file) : null;
	};

	const updateFileChildrens = (fileItemUploads: any, itemChildren: any, fieldChildrenName: string) => {
		itemChildren[fieldChildrenName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem: any) => fileItem.file) : null;
	};

	const formattedFormulaChildrenResults = (nameKey: string, itemChildrens: any, formData?: any) => {
		const items = itemChildrens[nameKey];
		if (!items) return [];

		items.forEach((item: any) => {
			// Kiểm tra nếu có original_formula và nó không phải là false
			if (!item.original_formula == false) {
				const calculatedValue = calculateFormulaWithFormData(item.original_formula, item, formData);
				item[item.keyword_formula] = numberCommas(calculatedValue); // Format và cập nhật kết quả
			}
		});

		return items;
	};

	return {
		transformObject,
		isJsonArrayOrObject,
		mappedTypeDataField,
		converFormFields,
		calculateFormulaWithFormData,
		removeCommas,
		extractSumPlus,
		sumColumn,
		initializeItem,
		initializeItemChildrens,
		initializeValues,
		addItem,
		removeItem,
		showSubColumnTable,
		showSubColumnTableChildren,
		updateFiles,
		updateFileChildrens,
		formattedFormulaChildrenResults
	}
}
