import { createRouter, createWebHistory } from 'vue-router';
import DefaultLayout from '@/layouts/DefaultLayout.vue';
import { authGuard } from './guards/auth';
import { resolveRouteTitle } from './titleResolver';
import { languageEventBus } from '@/utils/languageUtils';

// Khóa i18n cho các route
const routeKeys = {
	home: 'home',
	dashboard: 'dashboard',
	login: 'auth.login',
	resetPassword: 'auth.reset_password',
	newPassword: 'auth.new_pass',
	page404: 'page_404',
	page403: 'page_403',
	jobList: 'job.list',
	jobAdd: 'job.add',
	jobDetail: 'job.detail',
	workflowList: 'workflow.list',
	workflowAdd: 'workflow.add',
	workflowDetail: 'workflow.detail',
	permissionList: 'permission.list',
	roleList: 'role.list',
	roleAdd: 'role.add',
	userList: 'user.list',
	userAdd: 'user.add',
};

const routes = [
	{
		path: '/login',
		name: 'login',
		component: () => import(/* webpackChunkName: "login" */ '@/views/pages/auth/Login.vue'),
		meta: { 
			titleKey: routeKeys.login 
		}
	},
	{
		path: '/reset-password',
		name: 'ResetPassword',
		component: () => import(/* webpackChunkName: "reset-password" */ '@/views/pages/auth/ResetPassword.vue'),
		meta: { 
			titleKey: routeKeys.resetPassword,
		}
	},
	{
		path: '/new-password',
		name: 'NewPassword',
		component: () => import(/* webpackChunkName: "new-password" */ '@/views/pages/auth/NewPassword.vue'),
		meta: { 
			titleKey: routeKeys.newPassword,
		}
	},
	{
		path: '/:catchAll(.*)',
		name: 'Page404',
		component: () => import(/* webpackChunkName: "404" */ '@/views/pages/Page404.vue'),
		meta: { 
			titleKey: routeKeys.page404 
		}
	},
	{
		path: '/403',
		name: 'Page403',
		component: () => import(/* webpackChunkName: "403" */ '@/views/pages/Page403.vue'),
		meta: { 
			titleKey: routeKeys.page403 
		}
	},
	{
		path: '/',
		component: DefaultLayout,
		redirect: '/home',
		meta: {
			requiresAuth: true,  // All routes under this layout require authentication
		},
		children: [
			{
				path: 'home',
				name: 'Home',
				component: () => import(/* webpackChunkName: "home" */ '@/views/home/<USER>'),
				meta: {
					titleKey: routeKeys.home,
				},
			},
			{
				path: 'dashboard',
				name: 'Dashboard',
				component: () => import(/* webpackChunkName: "dashboard" */ '@/views/dashboard/Dashboard.vue'),
				meta: {
					titleKey: routeKeys.dashboard,
					permissions: ['VIEW-DASHBOARD'],
				},
			},
			{
				path: '/job',
				name: 'JobListData',
				component: () => import(/* webpackChunkName: "job-list" */ '@/views/job/JobListData.vue'),
				meta: {
					titleKey: routeKeys.jobList,
					permissions: ['VIEW-JOB'],
				}
			},
			{
				path: '/job/add',
				name: 'JobAdd',
				component: () => import(/* webpackChunkName: "job-add" */ '@/views/job/JobAdd.vue'),
				meta: {
					titleKey: routeKeys.jobAdd,
					permissions: ['CREATE-JOB'],
				}
			},
			{
				path: '/job/add-backup',
				name: 'JobAddBackup',
				component: () => import(/* webpackChunkName: "job-add-backup" */ '@/views/job/JobAddBackup.vue'),
				meta: {
					titleKey: routeKeys.jobAdd,
					permissions: ['CREATE-JOB'],
				}
			},
			{
				path: '/job/:id',
				name: 'JobDetail',
				component: () => import(/* webpackChunkName: "job-detail" */ '@/views/job/JobDetail.vue'),
				meta: {
					titleKey: routeKeys.jobDetail,
					permissions: ['VIEW-JOB'],
				}
			},
			{
				path: '/workflow',
				name: 'WorkflowListData',
				component: () => import(/* webpackChunkName: "workflow-list" */ '@/views/workflow/WorkflowListData.vue'),
				meta: {
					titleKey: routeKeys.workflowList,
					permissions: ['VIEW-WORKFLOW'],
				}
			},
			{           
				path: '/workflow/add',
				name: 'WorkflowAdd',
				component: () => import(/* webpackChunkName: "workflow-add" */ '@/views/workflow/WorkflowAdd.vue'),
				meta: {
					titleKey: routeKeys.workflowAdd,
					permissions: ['CREATE-WORKFLOW'],
				}
			},
			{
				path: '/workflow/:id',
				name: 'WorkflowDetail',
				component: () => import(/* webpackChunkName: "workflow-detail" */ '@/views/workflow/WorkflowDetail.vue'),
				meta: {
					titleKey: routeKeys.workflowDetail,
					permissions: ['VIEW-WORKFLOW'],
				}
			},
			{
				path: '/admin-permission',
				name: 'PermissionListData',
				component: () => import(/* webpackChunkName: "permission-list" */ '@/views/admin/permission/PermissionListData.vue'),
				meta: {
					titleKey: routeKeys.permissionList,
					permissions: ['SUPPER-ADMIN-SETTING'],
				}
			},
			{
				path: '/admin-role',
				name: 'RoleListData',
				component: () => import(/* webpackChunkName: "role-list" */ '@/views/admin/role/RoleListData.vue'),
				meta: {
					titleKey: routeKeys.roleList,
					permissions: ['ADMIN-SETTING'],
				}
			},
			{
				path: '/admin-role/add',
				name: 'RoleAdd',
				component: () => import(/* webpackChunkName: "role-add" */ '@/views/admin/role/RoleAdd.vue'),
				meta: {
					titleKey: routeKeys.roleAdd,
					permissions: ['ADMIN-SETTING'],
				}
			},
			{
				path: '/admin-user',
				name: 'UserListData',
				component: () => import(/* webpackChunkName: "user-list" */ '@/views/admin/user/UserListData.vue'),
				meta: {
					titleKey: routeKeys.userList,
					permissions: ['ADMIN-SETTING'],
				}
			},
			{
				path: '/admin-user/add',
				name: 'UserAdd',
				component: () => import(/* webpackChunkName: "user-add" */ '@/views/admin/user/UserAdd.vue'),
				meta: {
					titleKey: routeKeys.userAdd,
					permissions: ['ADMIN-SETTING'],
				}
			},
		],
	},
];

const router = createRouter({
	history: createWebHistory(),
	routes,
	scrollBehavior() {
		// always scroll to top
		return { top: 0 };
	},
});

// Register global navigation guard
router.beforeEach(authGuard);

// Cập nhật tiêu đề trang dựa trên titleKey khi route thay đổi
router.afterEach((to) => {
	if (to.meta.titleKey) {
		const title = resolveRouteTitle(to.meta.titleKey as string);
		document.title = title || 'TVNAS';
	}
});

// Lắng nghe sự kiện thay đổi ngôn ngữ để cập nhật tiêu đề trang
languageEventBus.onLanguageChanged(() => {
	// Cập nhật tiêu đề cho route hiện tại
	const currentRoute = router.currentRoute.value;
	if (currentRoute.meta.titleKey) {
		const title = resolveRouteTitle(currentRoute.meta.titleKey as string);
		document.title = title || 'TVNAS';
	}
});

export default router;
